'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { IPackageReview } from "@/types/package_"
import { Edit, Trash2 } from 'lucide-react'
import Image from "next/image"

export function ReviewList({
  items,
  onEdit,
  onDelete,
}: {
  items: IPackageReview[],
  onEdit: (id: string) => void,
  onDelete: (id: string) => void
}) {
  if (items.length === 0) {
    return (
      <Card>
        <CardHeader><CardTitle>Review List</CardTitle></CardHeader>
        <CardContent className="text-center text-gray-500">
          No reviews yet.
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader><CardTitle>Review List</CardTitle></CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Image</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Rating</TableHead>
                <TableHead>Comment</TableHead>
                <TableHead>Publish</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {items.map(item => (
                <TableRow key={item.id}>
                  <TableCell>
                    {item.reviewImage ? (
                      <Image src={item.reviewImage} alt={item.name} width={50} height={50} className="rounded object-cover" />
                    ) : (
                      <span className="text-gray-400 text-sm">No Image</span>
                    )}
                  </TableCell>
                  <TableCell>{item.name}</TableCell>
                  <TableCell>{item.email}</TableCell>
                  <TableCell>{item.rating}</TableCell>
                  <TableCell>{item.comment}</TableCell>
                  <TableCell>{item.published ? "Yes" : "No"}</TableCell>
                  <TableCell>
                    <Button size="sm" variant="outline" onClick={() => onEdit(item.id)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="outline" className="ml-1 text-red-600" onClick={() => onDelete(item.id)}>
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}
